{"name": "@hcengineering/desktop", "version": "0.6.465", "main": "dist/main/electron.js", "template": "@hcengineering/webpack-package", "scripts": {"build": "compile", "build:watch": "tsc", "_phase:package": "rushx package", "_phase:validate": "compile validate", "_phase:test": "jest --passWithNoTests --silent --forceExit --detectOpenHandles", "package": "rushx bump && cross-env MODEL_VERSION=$(node ../common/scripts/show_version.js) VERSION=$(node ../common/scripts/show_tag.js) NODE_ENV=production NODE_OPTIONS='--max-old-space-size=4094' webpack --stats-error-details && echo 'done'", "webpack": "cross-env MODEL_VERSION=$(node ../common/scripts/show_version.js) VERSION=$(node ../common/scripts/show_tag.js) NODE_ENV=development webpack --stats-error-details --progress -w", "devp": "cross-env MODEL_VERSION=$(node ../common/scripts/show_version.js) VERSION=$(node ../common/scripts/show_tag.js) NODE_ENV=production CLIENT_TYPE=dev webpack --progress -w", "dev": "cross-env MODEL_VERSION=$(node ../common/scripts/show_version.js) VERSION=$(node ../common/scripts/show_tag.js) NODE_ENV=development webpack --progress -w", "start": "cross-env MODEL_VERSION=$(node ../common/scripts/show_version.js) VERSION=$(node ../common/scripts/show_tag.js) NODE_ENV=production electron --no-sandbox .", "start-dev": "cross-env MODEL_VERSION=$(node ../common/scripts/show_version.js) VERSION=$(node ../common/scripts/show_tag.js) NODE_ENV=development electron --no-sandbox .", "test": "jest --passWithNoTests --silent --forceExit --verbose --detectOpenHandles", "test:debug": "node --inspect-brk ./node_modules/jest/bin/jest.js --runInBand --no-cache", "format": "format", "bump": "bump-package-version"}, "devDependencies": {"@hcengineering/platform-rig": "^0.6.0", "@vercel/webpack-asset-relocator-loader": "^1.7.3", "node-loader": "~2.0.0", "cross-env": "~7.0.3", "webpack-cli": "^5.1.4", "webpack": "^5.97.1", "mini-css-extract-plugin": "^2.2.0", "dotenv-webpack": "^8.0.1", "ts-loader": "^9.2.5", "svelte-loader": "^3.2.0", "css-loader": "^5.2.1", "webpack-dev-server": "^4.11.1", "style-loader": "^3.3.1", "file-loader": "^6.2.0", "sass-loader": "^13.2.0", "webpack-bundle-analyzer": "^4.10.2", "svgo-loader": "^3.0.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.20", "postcss-loader": "^7.0.2", "postcss-load-config": "^4.0.1", "compression-webpack-plugin": "^10.0.0", "html-webpack-plugin": "^5.5.0", "fork-ts-checker-webpack-plugin": "^9.0.2", "update-browserslist-db": "^1.1.3", "browserslist": "^4.25.0", "typescript": "^5.8.3", "ts-node": "^10.8.0", "ts-node-dev": "^2.0.0", "electron": "^36.3.1", "@types/node": "^22.15.29", "copy-webpack-plugin": "^11.0.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "eslint-config-standard-with-typescript": "^40.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.4.0", "eslint-plugin-promise": "^6.1.1", "eslint": "^8.54.0", "prettier": "^3.1.0", "esbuild": "^0.24.2", "esbuild-loader": "^4.0.3", "svelte-preprocess": "^5.1.3", "@types/ws": "^8.5.11", "jest": "^29.7.0", "ts-jest": "^29.1.1", "@types/jest": "^29.5.5", "@testing-library/jest-dom": "^6.6.3"}, "dependencies": {"@hcengineering/platform": "^0.6.11", "@hcengineering/ui": "^0.6.15", "@hcengineering/theme": "^0.6.5", "@hcengineering/login": "^0.6.12", "@hcengineering/login-assets": "^0.6.0", "@hcengineering/login-resources": "^0.6.2", "@hcengineering/onboard": "^0.6.0", "@hcengineering/onboard-assets": "^0.6.0", "@hcengineering/onboard-resources": "^0.6.0", "@hcengineering/client": "^0.6.18", "@hcengineering/workbench": "^0.6.16", "@hcengineering/workbench-resources": "^0.6.1", "@hcengineering/view": "^0.6.13", "@hcengineering/view-assets": "^0.6.11", "@hcengineering/view-resources": "^0.6.0", "@hcengineering/contact": "^0.6.24", "@hcengineering/contact-resources": "^0.6.0", "@hcengineering/task": "^0.6.20", "@hcengineering/task-assets": "^0.6.19", "@hcengineering/task-resources": "^0.6.0", "@hcengineering/chunter": "^0.6.20", "@hcengineering/chunter-assets": "^0.6.18", "@hcengineering/chunter-resources": "^0.6.0", "@hcengineering/recruit": "^0.6.29", "@hcengineering/recruit-assets": "^0.6.23", "@hcengineering/recruit-resources": "^0.6.0", "@hcengineering/setting": "^0.6.17", "@hcengineering/setting-assets": "^0.6.15", "@hcengineering/setting-resources": "^0.6.0", "@hcengineering/client-resources": "^0.6.27", "@hcengineering/contact-assets": "^0.6.13", "@hcengineering/activity": "^0.6.0", "@hcengineering/activity-assets": "^0.6.3", "@hcengineering/activity-resources": "^0.6.1", "@hcengineering/telegram": "^0.6.21", "@hcengineering/telegram-assets": "^0.6.0", "@hcengineering/telegram-resources": "^0.6.0", "@hcengineering/workbench-assets": "^0.6.14", "@hcengineering/attachment": "^0.6.14", "@hcengineering/attachment-assets": "^0.6.11", "@hcengineering/attachment-resources": "^0.6.0", "@hcengineering/lead": "^0.6.0", "@hcengineering/lead-assets": "^0.6.0", "@hcengineering/lead-resources": "^0.6.0", "@hcengineering/gmail": "^0.6.22", "@hcengineering/gmail-assets": "^0.6.0", "@hcengineering/gmail-resources": "^0.6.0", "@hcengineering/image-cropper": "^0.6.0", "@hcengineering/image-cropper-resources": "^0.6.0", "@hcengineering/inventory": "^0.6.11", "@hcengineering/inventory-assets": "^0.6.11", "@hcengineering/inventory-resources": "^0.6.0", "@hcengineering/templates": "^0.6.11", "@hcengineering/templates-assets": "^0.6.11", "@hcengineering/templates-resources": "^0.6.0", "@hcengineering/notification": "^0.6.23", "@hcengineering/notification-assets": "^0.6.17", "@hcengineering/notification-resources": "^0.6.0", "@hcengineering/preference": "^0.6.13", "@hcengineering/preference-assets": "^0.6.0", "@hcengineering/core": "^0.6.32", "@hcengineering/rekoni": "^0.6.0", "@hcengineering/tags-assets": "^0.6.0", "@hcengineering/tags": "^0.6.16", "@hcengineering/tags-resources": "^0.6.0", "@hcengineering/calendar": "^0.6.24", "@hcengineering/calendar-assets": "^0.6.22", "@hcengineering/calendar-resources": "^0.6.0", "@hcengineering/presentation": "^0.6.3", "@hcengineering/tracker": "^0.6.24", "@hcengineering/tracker-assets": "^0.6.0", "@hcengineering/tracker-resources": "^0.6.0", "@hcengineering/text-editor": "^0.6.0", "@hcengineering/text-editor-assets": "^0.6.0", "@hcengineering/text-editor-resources": "^0.6.0", "@hcengineering/board": "^0.6.19", "@hcengineering/board-assets": "^0.6.19", "@hcengineering/board-resources": "^0.6.0", "@hcengineering/hr": "^0.6.19", "@hcengineering/hr-assets": "^0.6.19", "@hcengineering/hr-resources": "^0.6.0", "@hcengineering/bitrix": "^0.6.52", "@hcengineering/bitrix-assets": "^0.6.0", "@hcengineering/bitrix-resources": "^0.6.0", "@hcengineering/request": "^0.6.14", "@hcengineering/request-assets": "^0.6.0", "@hcengineering/request-resources": "^0.6.0", "@hcengineering/drive": "^0.6.0", "@hcengineering/drive-assets": "^0.6.0", "@hcengineering/drive-resources": "^0.6.0", "@hcengineering/support": "^0.6.5", "@hcengineering/support-assets": "^0.6.5", "@hcengineering/support-resources": "^0.6.0", "@hcengineering/diffview": "^0.6.0", "@hcengineering/diffview-assets": "^0.6.0", "@hcengineering/diffview-resources": "^0.6.0", "@hcengineering/time": "^0.6.0", "@hcengineering/time-assets": "^0.6.0", "@hcengineering/time-resources": "^0.6.0", "@hcengineering/github": "^0.6.0", "@hcengineering/github-assets": "^0.6.0", "@hcengineering/github-resources": "^0.6.0", "@hcengineering/desktop-preferences": "^0.6.0", "@hcengineering/desktop-preferences-assets": "^0.6.0", "@hcengineering/desktop-preferences-resources": "^0.6.0", "@hcengineering/desktop-downloads": "^0.6.0", "@hcengineering/desktop-downloads-assets": "^0.6.0", "@hcengineering/desktop-downloads-resources": "^0.6.0", "@hcengineering/document": "^0.6.0", "@hcengineering/document-assets": "^0.6.0", "@hcengineering/document-resources": "^0.6.0", "@hcengineering/love": "^0.6.0", "@hcengineering/love-assets": "^0.6.0", "@hcengineering/love-resources": "^0.6.0", "@hcengineering/sign": "^0.6.0", "@hcengineering/print": "^0.6.0", "@hcengineering/print-assets": "^0.6.0", "@hcengineering/print-resources": "^0.6.0", "@hcengineering/guest": "^0.6.4", "@hcengineering/guest-assets": "^0.6.0", "@hcengineering/guest-resources": "^0.6.0", "@hcengineering/presence": "^0.6.0", "@hcengineering/presence-resources": "^0.6.0", "@hcengineering/media": "^0.6.0", "@hcengineering/media-assets": "^0.6.0", "@hcengineering/media-resources": "^0.6.0", "@hcengineering/recorder": "^0.6.0", "@hcengineering/recorder-assets": "^0.6.0", "@hcengineering/recorder-resources": "^0.6.0", "@hcengineering/uploader": "^0.6.0", "@hcengineering/uploader-assets": "^0.6.0", "@hcengineering/uploader-resources": "^0.6.0", "@hcengineering/controlled-documents": "^0.1.0", "@hcengineering/controlled-documents-assets": "^0.1.0", "@hcengineering/controlled-documents-resources": "^0.1.0", "@hcengineering/questions": "^0.1.0", "@hcengineering/questions-assets": "^0.1.0", "@hcengineering/questions-resources": "^0.1.0", "@hcengineering/training": "^0.1.0", "@hcengineering/training-assets": "^0.1.0", "@hcengineering/training-resources": "^0.1.0", "@hcengineering/server-training": "^0.1.0", "@hcengineering/server-training-resources": "^0.1.0", "@hcengineering/products": "^0.1.0", "@hcengineering/products-assets": "^0.1.0", "@hcengineering/products-resources": "^0.1.0", "@hcengineering/process": "^0.6.0", "@hcengineering/process-assets": "^0.6.0", "@hcengineering/process-resources": "^0.6.0", "@hcengineering/analytics-providers": "^0.6.0", "@hcengineering/analytics-collector": "^0.6.0", "@hcengineering/analytics-collector-assets": "^0.6.0", "@hcengineering/analytics-collector-resources": "^0.6.0", "@hcengineering/ai-bot": "^0.6.0", "@hcengineering/ai-bot-resources": "^0.6.0", "@hcengineering/test-management": "^0.6.0", "@hcengineering/test-management-assets": "^0.6.0", "@hcengineering/test-management-resources": "^0.6.0", "@hcengineering/survey": "^0.6.0", "@hcengineering/survey-assets": "^0.6.0", "@hcengineering/survey-resources": "^0.6.0", "@hcengineering/card": "^0.6.0", "@hcengineering/card-assets": "^0.6.0", "@hcengineering/card-resources": "^0.6.0", "@hcengineering/export": "^0.6.0", "@hcengineering/export-assets": "^0.6.0", "@hcengineering/export-resources": "^0.6.0", "@hcengineering/mail": "^0.6.0", "@hcengineering/mail-assets": "^0.6.0", "@hcengineering/chat": "^0.6.0", "@hcengineering/chat-assets": "^0.6.0", "@hcengineering/chat-resources": "^0.6.0", "@hcengineering/inbox": "^0.6.0", "@hcengineering/inbox-assets": "^0.6.0", "@hcengineering/inbox-resources": "^0.6.0", "@hcengineering/achievement": "^0.6.0", "@hcengineering/achievement-assets": "^0.6.0", "@hcengineering/achievement-resources": "^0.6.0", "@hcengineering/communication": "^0.6.0", "@hcengineering/communication-assets": "^0.6.0", "@hcengineering/communication-resources": "^0.6.0", "@hcengineering/emoji": "^0.6.0", "@hcengineering/emoji-assets": "^0.6.0", "@hcengineering/emoji-resources": "^0.6.0", "@hcengineering/billing": "^0.6.0", "@hcengineering/billing-assets": "^0.6.0", "@hcengineering/billing-resources": "^0.6.0", "electron-squirrel-startup": "~1.0.0", "dotenv": "~16.0.0", "electron-context-menu": "^4.0.4", "electron-windows-badge": "^1.1.0", "svelte": "^4.2.19", "commander": "^8.1.0", "electron-store": "^8.2.0", "electron-log": "^5.1.7", "electron-updater": "^6.3.4", "livekit-client": "^2.13.3", "@hcengineering/server-backup": "^0.6.0", "@hcengineering/communication-types": "^0.1.0", "ws": "^8.18.2"}, "productName": "<PERSON><PERSON>", "description": "<PERSON>ly Desktop experience", "keywords": ["electron", "typescript", "svelte"]}