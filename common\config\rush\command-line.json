/**
 * This configuration file defines custom commands for the "rush" command-line.
 * More documentation is available on the Rush website: https://rushjs.io
 */
{
  "$schema": "https://developer.microsoft.com/json-schemas/rush/v5/command-line.schema.json",

  "phases": [
    {
      "name": "_phase:build",
      "dependencies": {
        "upstream": ["_phase:build"]
      },
      "ignoreMissingScript": true,
      "allowWarningsOnSuccess": false
    },
    {
      "name": "_phase:validate",
      "dependencies": {
        "self": ["_phase:build"],
        "upstream": ["_phase:validate", "_phase:build"]
      },
      "ignoreMissingScript": true,
      "allowWarningsOnSuccess": false
    },
    {
      "name": "_phase:test",
      "dependencies": {
        "self": ["_phase:build"],
        "upstream": ["_phase:validate"]
      },
      "ignoreMissingScript": true,
      "allowWarningsOnSuccess": true
    },
    {
      "name": "_phase:lint",
      "dependencies": {
        "self": ["_phase:build"]
      },
      "ignoreMissingScript": true,
      "allowWarningsOnSuccess": false
    },
    {
      "name": "_phase:bundle",
      "dependencies": {
        "self": ["_phase:build"]
      },
      "ignoreMissingScript": true,
      "allowWarningsOnSuccess": false
    },
    {
      "name": "_phase:format",
      "dependencies": {
        "self": ["_phase:build"]
      },
      "ignoreMissingScript": true,
      "allowWarningsOnSuccess": false
    },
    {
      "name": "_phase:svelte-check",
      "dependencies": {
        "self": ["_phase:build"]
      },
      "ignoreMissingScript": true,
      "allowWarningsOnSuccess": true
    },
    {
      "name": "_phase:package",
      "dependencies": {
        "self": ["_phase:build"],
        "upstream": ["_phase:package"]
      },
      "ignoreMissingScript": true,
      "allowWarningsOnSuccess": false
    },
    {
      "name": "_phase:docker-build",
      "dependencies": {
        "self": ["_phase:build", "_phase:package", "_phase:bundle"],
        "upstream": ["_phase:build", "_phase:bundle", "_phase:package"]
      },
      "ignoreMissingScript": true,
      "allowWarningsOnSuccess": true
    },
    {
      "name": "_phase:docker-staging",
      "dependencies": {
        "self": ["_phase:build", "_phase:package", "_phase:bundle"]
      },
      "ignoreMissingScript": true,
      "allowWarningsOnSuccess": true
    }
  ],
  "commands": [
    {
      "commandKind": "bulk",
      "name": "format",
      "summary": "Format",
      "description": "Perform a formatting",
      "enableParallelism": true,
      "incremental": false,
      "ignoreMissingScript": true,
      "safeForSimultaneousRushProcesses": true,
      "disableBuildCache": true
    },
    {
      "commandKind": "global",
      "name": "doformat",
      "summary": "Do a format and show errors after it",
      "description": "Do a format and show errors after it",
      "safeForSimultaneousRushProcesses": true,
      "shellCommand": "(rush format && true) && ./common/scripts/format-show.sh"
    },
    {
      "commandKind": "phased",
      "name": "build:watch",
      "summary": "Build and watch",
      "phases": ["_phase:build", "_phase:validate"],
      "description": "Perform build with tsc and watch for changes with rush",
      "enableParallelism": true,
      "incremental": true,
      "safeForSimultaneousRushProcesses": true,
      "watchOptions": {
        "alwaysWatch": true,
        "watchPhases": ["_phase:build", "_phase:validate"]
      }
    },
    {
      "summary": "svelte-check",
      "commandKind": "phased",
      "name": "svelte-check",
      "phases": ["_phase:build", "_phase:svelte-check"],
      "enableParallelism": true,
      "incremental": true
    },
    {
      "commandKind": "phased",
      "name": "build",
      "summary": "build",
      "phases": ["_phase:build"],
      "enableParallelism": true,
      "incremental": true,
      "watchOptions": {
        "alwaysWatch": false,
        "watchPhases": ["_phase:build"]
      }
    },
    {
      "commandKind": "phased",
      "name": "validate",
      "phases": ["_phase:validate"],
      "summary": "validate",
      "enableParallelism": true,
      "incremental": true
    },
    {
      "commandKind": "phased",
      "name": "rebuild",
      "summary": "ReBuild and test all projects.",
      "phases": ["_phase:build"],
      "enableParallelism": true,
      "incremental": false
    },
    {
      "commandKind": "phased",
      "name": "dorevalidate",
      "phases": ["_phase:validate"],
      "summary": "dorevalidate",
      "enableParallelism": true,
      "incremental": false
    },
    {
      "commandKind": "phased",
      "summary": "Do testing",
      "name": "test",
      "phases": ["_phase:build", "_phase:test"],
      "enableParallelism": true,
      "incremental": true
    },
    {
      "commandKind": "phased",
      "name": "retest",
      "summary": "Build and test all projects.",
      "phases": ["_phase:build", "_phase:test"],
      "enableParallelism": true,
      "incremental": false
    },

    {
      "commandKind": "phased",
      "summary": "Do bundle",
      "name": "bundle",
      "phases": ["_phase:build", "_phase:bundle"],
      "enableParallelism": true,
      "incremental": true
    },
    {
      "commandKind": "phased",
      "summary": "Do packaging",
      "name": "package",
      "phases": ["_phase:build", "_phase:package"],
      "enableParallelism": true,
      "incremental": true
    },

    {
      "summary": "docker:build",
      "commandKind": "phased",
      "name": "docker:build",
      "phases": ["_phase:build", "_phase:bundle", "_phase:package", "_phase:docker-build"],
      "enableParallelism": true,
      "incremental": true
    },
    {
      "summary": "docker:rebuild",
      "commandKind": "phased",
      "name": "docker:rebuild",
      "phases": ["_phase:build", "_phase:bundle", "_phase:package", "_phase:docker-build"],
      "enableParallelism": true,
      "incremental": false
    },
    {
      "summary": "docker:staging",
      "commandKind": "phased",
      "name": "docker:staging",
      "phases": ["_phase:build", "_phase:bundle", "_phase:package", "_phase:docker-staging"],
      "enableParallelism": true,
      "incremental": true
    },
    {
      "commandKind": "bulk",
      "name": "docker:push",
      "summary": "docker:push",
      "description": "Push docker release images",
      "enableParallelism": true,
      "incremental": false,
      "ignoreDependencyOrder": false,
      "ignoreMissingScript": true,
      "disableBuildCache": true,
      "allowWarningsInSuccessfulBuild": true
    },
    {
      "commandKind": "global",
      "name": "docker",
      "summary": "Build docker with platform",
      "description": "use to build all docker containers required for platform",
      "safeForSimultaneousRushProcesses": true,
      "shellCommand": "./common/scripts/docker.sh"
    },
    {
      "commandKind": "global",
      "name": "docker:up",
      "summary": "Up development build",
      "description": "Up development build",
      "safeForSimultaneousRushProcesses": true,
      "shellCommand": "cd ./dev && docker compose up -d --force-recreate"
    },
    {
      "commandKind": "global",
      "name": "docker:local",
      "summary": "Up development build",
      "description": "Up development build",
      "safeForSimultaneousRushProcesses": true,
      "shellCommand": "cd ./dev/local-mongo && docker compose -p dev up -d --force-recreate"
    },
    {
      "commandKind": "global",
      "name": "tool:upgrade",
      "summary": "Upgrade all local models",
      "description": "Upgrade all local models",
      "safeForSimultaneousRushProcesses": true,
      "shellCommand": "cd ./dev/tool && rushx run-local upgrade -f"
    },
    {
      "commandKind": "global",
      "name": "apply-templates",
      "summary": "Update all package.json according to templates matched from templates folder",
      "description": "Use to update all projects to templates",
      "safeForSimultaneousRushProcesses": true,
      "shellCommand": "node templates/apply.js"
    },
    {
      "commandKind": "global",
      "name": "ts-clean",
      "summary": "Clean tsconfig.tsbuildinfo",
      "description": "Clean typescript incremental cache",
      "safeForSimultaneousRushProcesses": true,
      "shellCommand": "find .|grep tsconfig.tsbuildinfo | xargs rm | pwd"
    },
    {
      "commandKind": "global",
      "name": "revalidate",
      "summary": "Clean Validate cache and to validate again",
      "description": "Clean typescript incremental cache",
      "safeForSimultaneousRushProcesses": true,
      "shellCommand": "find .|grep tsBuildInfoFile.info | xargs rm | pwd && rush dorevalidate"
    },
    {
      "commandKind": "bulk",
      "name": "remove-ts-types",
      "summary": "Clean validate types",
      "description": "Clean validate types",
      "shellCommand": "rm -rf ./types",
      "enableParallelism": true,
      "incremental": false,
      "ignoreDependencyOrder": true,
      "ignoreMissingScript": true,
      "disableBuildCache": true,
      "allowWarningsInSuccessfulBuild": true
    },
    {
      "commandKind": "global",
      "name": "model-version",
      "summary": "show model version",
      "description": "show model version",
      "safeForSimultaneousRushProcesses": true,
      "shellCommand": "npx node ./common/scripts/show_version.js"
    },
    {
      "commandKind": "global",
      "name": "show-model",
      "summary": "show model",
      "description": "show model",
      "safeForSimultaneousRushProcesses": true,
      "shellCommand": "cd ./models/all && rushx show-model"
    },
    {
      "commandKind": "global",
      "name": "deps-clean",
      "summary": "Clean package-deps-*.json files",
      "description": "Clean package-deps-*.json files",
      "safeForSimultaneousRushProcesses": true,
      "shellCommand": "find .|grep .rush/temp/package-deps_ | xargs rm"
    },
    {
      "commandKind": "global",
      "name": "fast-format",
      "summary": "Format changed projects",
      "description": "Format and autofix linting issues in changed projects",
      "safeForSimultaneousRushProcesses": true,
      "shellCommand": "./common/scripts/fast-format.sh"
    },
    {
      "commandKind": "global",
      "name": "desktop",
      "summary": "Build desktop build",
      "description": "use to build all docker containers required for platform",
      "safeForSimultaneousRushProcesses": true,
      "shellCommand": "rush package -p 20 --to desktop && cd ./desktop-package && rushx dist"
    }
  ],

  /**
   * Custom "parameters" introduce new parameters for specified Rush command-line commands.
   * For example, you might define a "--production" parameter for the "rush build" command.
   */
  "parameters": [
    {
      "parameterKind": "flag",
      "longName": "--lite",
      "shortName": "-l",
      "description": "Enable Heft lite building option, will skip some phases.",
      "associatedCommands": ["build", "rebuild"]
    },
    {
      "parameterKind": "flag",
      "longName": "--clean",
      "description": "Enable Heft clean building option",
      "associatedCommands": ["build", "rebuild"]
    },
    {
      "parameterKind": "flag",
      "longName": "--force",
      "shortName": "-f",
      "description": "Force formatting",
      "associatedCommands": ["format"]
    },
    {
      "parameterKind": "string",
      "argumentName": "BRANCH",
      "required": false,
      "associatedPhases": [],
      "shortName": "-b",
      "longName": "--branch",
      "description": "Force formatting of branch",
      "associatedCommands": ["fast-format"]
    }
    // {
    //   /**
    //    * (Required) Determines the type of custom parameter.
    //    * A "flag" is a custom command-line parameter whose presence acts as an on/off switch.
    //    */
    //   "parameterKind": "flag",
    //
    //   /**
    //    * (Required) The long name of the parameter.  It must be lower-case and use dash delimiters.
    //    */
    //   "longName": "--my-flag",
    //
    //   /**
    //    * An optional alternative short name for the parameter.  It must be a dash followed by a single
    //    * lower-case or upper-case letter, which is case-sensitive.
    //    *
    //    * NOTE: The Rush developers recommend that automation scripts should always use the long name
    //    * to improve readability.  The short name is only intended as a convenience for humans.
    //    * The alphabet letters run out quickly, and are difficult to memorize, so *only* use
    //    * a short name if you expect the parameter to be needed very often in everyday operations.
    //    */
    //   "shortName": "-m",
    //
    //   /**
    //    * (Required) A long description to be shown in the command-line help.
    //    *
    //    * Whenever you introduce commands/parameters, taking a little time to write meaningful
    //    * documentation can make a big difference for the developer experience in your repo.
    //    */
    //   "description": "A custom flag parameter that is passed to the scripts that are invoked when building projects",
    //
    //   /**
    //    * (Required) A list of custom commands and/or built-in Rush commands that this parameter may
    //    * be used with.  The parameter will be appended to the shell command that Rush invokes.
    //    */
    //   "associatedCommands": ["build", "rebuild"]
    // },
    //
    // {
    //   /**
    //    * (Required) Determines the type of custom parameter.
    //    * A "string" is a custom command-line parameter whose value is a simple text string.
    //    */
    //   "parameterKind": "string",
    //   "longName": "--my-string",
    //   "description": "A custom string parameter for the \"my-global-command\" custom command",
    //
    //   "associatedCommands": ["my-global-command"],
    //
    //   /**
    //    * The name of the argument, which will be shown in the command-line help.
    //    *
    //    * For example, if the parameter name is '--count" and the argument name is "NUMBER",
    //    * then the command-line help would display "--count NUMBER".  The argument name must
    //    * be comprised of upper-case letters, numbers, and underscores.  It should be kept short.
    //    */
    //   "argumentName": "SOME_TEXT",
    //
    //   /**
    //    * If true, this parameter must be included with the command.  The default is false.
    //    */
    //   "required": false
    // },
    //
    // {
    //   /**
    //    * (Required) Determines the type of custom parameter.
    //    * A "choice" is a custom command-line parameter whose argument must be chosen from a list of
    //    * allowable alternatives.
    //    */
    //   "parameterKind": "choice",
    //   "longName": "--my-choice",
    //   "description": "A custom choice parameter for the \"my-global-command\" custom command",
    //
    //   "associatedCommands": ["my-global-command"],
    //
    //   /**
    //    * If true, this parameter must be included with the command.  The default is false.
    //    */
    //   "required": false,
    //
    //   /**
    //    * Normally if a parameter is omitted from the command line, it will not be passed
    //    * to the shell command. this value will be inserted by default.  Whereas if a "defaultValue"
    //    * is defined, the parameter will always be passed to the shell command, and will use the
    //    * default value if unspecified.  The value must be one of the defined alternatives.
    //    */
    //   "defaultValue": "vanilla",
    //
    //   /**
    //    * (Required) A list of alternative argument values that can be chosen for this parameter.
    //    */
    //   "alternatives": [
    //     {
    //       /**
    //        * A token that is one of the alternatives that can be used with the choice parameter,
    //        * e.g. "vanilla" in "--flavor vanilla".
    //        */
    //       "name": "vanilla",
    //
    //       /**
    //        * A detailed description for the alternative that can be shown in the command-line help.
    //        *
    //        * Whenever you introduce commands/parameters, taking a little time to write meaningful
    //        * documentation can make a big difference for the developer experience in your repo.
    //        */
    //       "description": "Use the vanilla flavor (the default)"
    //     },
    //
    //     {
    //       "name": "chocolate",
    //       "description": "Use the chocolate flavor"
    //     },
    //
    //     {
    //       "name": "strawberry",
    //       "description": "Use the strawberry flavor"
    //     }
    //   ]
    // }
  ]
}
