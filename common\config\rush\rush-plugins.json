/**
 * This configuration file manages <PERSON>'s plugin feature.
 */
{
  "$schema": "https://developer.microsoft.com/json-schemas/rush/v5/rush-plugins.schema.json",
  "plugins": [
    /**
     * Each item configures a plugin to be loaded by <PERSON>.
     */
    // {
    //   /**
    //    * The name of the NPM package that provides the plugin.
    //    */
    //   "packageName": "@scope/my-rush-plugin",
    //   /**
    //    * The name of the plugin.  This can be found in the "pluginName"
    //    * field of the "rush-plugin-manifest.json" file in the NPM package folder.
    //    */
    //   "pluginName": "my-plugin-name",
    //   /**
    //    * The name of a Rush autoinstaller that will be used for installation, which
    //    * can be created using "rush init-autoinstaller".  Add the plugin's NPM package
    //    * to the package.json "dependencies" of your autoinstaller, then run
    //    * "rush update-autoinstaller".
    //    */
    //   "autoinstallerName": "rush-plugins"
    // }
  ]
}