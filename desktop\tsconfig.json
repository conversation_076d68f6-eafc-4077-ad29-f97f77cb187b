{"compilerOptions": {"outDir": "dist", "noImplicitAny": true, "module": "esnext", "target": "es2021", "allowJs": true, "declaration": true, "strictNullChecks": true, "sourceMap": true, "skipLibCheck": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "lib": ["es2023", "dom", "ES2021.String", "ESNext.Array"]}, "include": ["src/**/*", "declarations.d.ts"], "exclude": ["node_modules", "lib", "dist", "types", "bundle"]}