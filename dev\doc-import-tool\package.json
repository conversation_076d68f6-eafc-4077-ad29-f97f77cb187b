{"name": "@hcengineering/qms-doc-import-tool", "version": "0.1.0", "main": "lib/index.js", "svelte": "src/index.ts", "types": "types/index.d.ts", "author": "Anticrm Platform Contributors", "template": "@hcengineering/node-package", "scripts": {"build": "compile", "build:watch": "compile", "_phase:bundle": "rushx bundle", "bundle": "node esbuild.js", "run-local": "cross-env SERVER_SECRET=secret MONGO_URL=mongodb://localhost:27017 COLLABORATOR_URL=ws://localhost:3078 STORAGE_CONFIG=minio|minio?accessKey=minioadmin&secretKey=minioadmin node --nolazy -r ts-node/register ./src/__start.ts", "run": "cross-env node -r ts-node/register --max-old-space-size=8000 ./src/__start.ts", "format": "format src", "test": "jest --passWithNoTests --silent --forceExit", "_phase:build": "compile transpile src", "_phase:test": "jest --passWithNoTests --silent --forceExit", "_phase:format": "format src", "_phase:validate": "compile validate"}, "devDependencies": {"cross-env": "~7.0.3", "@hcengineering/platform-rig": "^0.6.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-n": "^15.4.0", "eslint": "^8.54.0", "ts-node": "^10.8.0", "esbuild": "^0.24.2", "@types/minio": "~7.0.11", "@types/node": "^22.15.29", "@typescript-eslint/parser": "^6.11.0", "eslint-config-standard-with-typescript": "^40.0.0", "prettier": "^3.1.0", "typescript": "^5.8.3", "jest": "^29.7.0", "ts-jest": "^29.1.1", "@types/jest": "^29.5.5", "@types/htmlparser2": "^3.10.7", "@types/domhandler": "^2.4.5"}, "dependencies": {"@hcengineering/controlled-documents": "^0.1.0", "@hcengineering/account": "^0.6.0", "@hcengineering/attachment": "^0.6.14", "@hcengineering/contact": "^0.6.24", "@hcengineering/core": "^0.6.32", "@hcengineering/platform": "^0.6.11", "@hcengineering/server-core": "^0.6.1", "@hcengineering/server-storage": "^0.6.0", "@hcengineering/server-token": "^0.6.11", "@hcengineering/server-tool": "^0.6.0", "@hcengineering/server-client": "^0.6.0", "@hcengineering/collaborator-client": "^0.6.4", "commander": "^8.1.0", "domhandler": "^5.0.3", "domutils": "^3.1.0", "htmlparser2": "^9.0.0", "mammoth": "^1.9.0", "docx4js": "^3.2.20", "zod": "^3.22.4"}}