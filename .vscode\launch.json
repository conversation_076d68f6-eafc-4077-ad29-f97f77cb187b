{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "address": "127.0.0.1",
      "localRoot": "${workspaceFolder}",
      "name": "Attach to Remote",
      "port": 9229,
      "request": "attach",
      "sourceMaps": true,
      "skipFiles": ["<node_internals>/**"],
      "type": "node"
    },
    {
      "name": "Python: Embeddings",
      "type": "python",
      "request": "launch",
      "program": "./pods/embeddings/server.py",
      "args": ["--model", "sentence-transformers/all-MiniLM-L6-v2", "--device", "cpu"],
      "console": "integratedTerminal",
      "justMyCode": true,
      "env": {
        "PYTORCH_ENABLE_MPS_FALLBACK": "1"
      }
    },
    {
      "name": "Debug server",
      "type": "node",
      "request": "launch",
      "args": ["src/__start.ts"],
      "env": {
        // "FULLTEXT_URL": "http://localhost:4700",
        "FULLTEXT_URL": "http://huly.local:4702",
        // "MONGO_URL": "mongodb://localhost:27017",
        "DB_URL": "mongodb://localhost:27017",
        // "DB_URL": "postgresql://postgres:example@localhost:5432",
        // "DB_URL": "postgresql://<EMAIL>:26257/defaultdb?sslmode=disable",
        // "GREEN_URL": "http://huly.local:6767?token=secret",
        "SERVER_PORT": "3333",
        "APM_SERVER_URL2": "http://localhost:8200",
        "METRICS_CONSOLE": "false",
        "METRICS_FILE": "${workspaceRoot}/metrics.txt", // Show metrics in console evert 30 seconds.,
        "STORAGE_CONFIG": "datalake|http://huly.local:4030",
        "SERVER_SECRET": "secret",
        "ENABLE_CONSOLE": "true",
        "COLLABORATOR_URL": "ws://localhost:3078",
        "REKONI_URL": "http://localhost:4004",
        "FRONT_URL": "http://localhost:8080",
        "ACCOUNTS_URL": "http://localhost:3000",
        "MODEL_JSON": "${workspaceRoot}/models/all/bundle/model.json",
        // "SERVER_PROVIDER":"uweb"
        "SERVER_PROVIDER": "ws",
        "MODEL_VERSION": "0.7.48",
        // "VERSION": "0.6.289",
        "ELASTIC_INDEX_NAME": "local_storage_index",
        "UPLOAD_URL": "/files",
        "AI_BOT_URL": "http://localhost:4010",
        "STATS_URL": "http://huly.local:4900",
        "STREAM_URL": "http://huly.local:1080/recording",
        "QUEUE_CONFIG": "localhost:19092"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "runtimeVersion": "20",
      "showAsyncStacks": true,
      "outputCapture": "std",
      "sourceMaps": true,
      "cwd": "${workspaceRoot}/pods/server",
      "protocol": "inspector"
    },
    {
      "name": "Debug server (CR)",
      "type": "node",
      "request": "launch",
      "args": ["src/__start.ts"],
      "env": {
        // "FULLTEXT_URL": "http://localhost:4700",
        "FULLTEXT_URL": "http://huly.local:4702",
        // "MONGO_URL": "mongodb://localhost:27017",
        // "DB_URL": "mongodb://localhost:27017",
        // "DB_URL": "postgresql://postgres:example@localhost:5432",
        "DB_URL": "postgresql://root@localhost:26257/defaultdb?sslmode=disable",
        // "GREEN_URL": "http://huly.local:6767?token=secret",
        "SERVER_PORT": "3332",
        "APM_SERVER_URL2": "http://localhost:8200",
        "METRICS_CONSOLE": "false",
        "METRICS_FILE": "${workspaceRoot}/metrics.txt", // Show metrics in console evert 30 seconds.,
        "STORAGE_CONFIG": "datalake|http://huly.local:4030",
        "SERVER_SECRET": "secret",
        "ENABLE_CONSOLE": "true",
        "COLLABORATOR_URL": "ws://localhost:3078",
        "REKONI_URL": "http://localhost:4004",
        "FRONT_URL": "http://localhost:8080",
        "ACCOUNTS_URL": "http://localhost:3000",
        "MODEL_JSON": "${workspaceRoot}/models/all/bundle/model.json",
        // "SERVER_PROVIDER":"uweb"
        "SERVER_PROVIDER": "ws",
        "MODEL_VERSION": "0.7.173",
        // "VERSION": "0.6.289",
        "COMMUNICATION_API_ENABLED": "true",
        "ELASTIC_INDEX_NAME": "local_storage_index",
        "UPLOAD_URL": "/files",
        "AI_BOT_URL": "http://localhost:4010",
        "STATS_URL": "http://huly.local:4900",
        "QUEUE_CONFIG": "localhost:19092",
        "FILES_URL": "http://huly.local:4030/blob/:workspace/:blobId/:filename"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "runtimeVersion": "20",
      "showAsyncStacks": true,
      "outputCapture": "std",
      "sourceMaps": true,
      "cwd": "${workspaceRoot}/pods/server",
      "protocol": "inspector"
    },
    {
      "name": "Debug server(Test)",
      "type": "node",
      "request": "launch",
      "args": ["src/__start.ts"],
      "env": {
        "FULLTEXT_URL": "http://localhost:4710",
        // "DB_URL": "mongodb://localhost:27018",
        // "DB_URL": "postgresql://postgres:example@localhost:5432",
        "DB_URL": "postgresql://<EMAIL>:26258/defaultdb?sslmode=disable",
        // "GREEN_URL": "http://huly.local:6767?token=secret",
        "SERVER_PORT": "3334",
        "METRICS_CONSOLE": "false",
        "DEBUG_PRINT_SQL": "true",
        "METRICS_FILE": "${workspaceRoot}/metrics.txt", // Show metrics in console evert 30 seconds.,
        "STORAGE_CONFIG": "minio|localhost?accessKey=minioadmin&secretKey=minioadmin",
        "SERVER_SECRET": "secret",
        "ENABLE_CONSOLE": "true",
        "COLLABORATOR_URL": "ws://localhost:3079",
        "FRONT_URL": "http://localhost:8083",
        "ACCOUNTS_URL": "http://localhost:3003",
        "MODEL_JSON": "${workspaceRoot}/models/all/bundle/model.json",
        "MODEL_VERSION": "0.7.173",
        "STATS_URL": "http://huly.local:4901",
        "QUEUE_CONFIG": "localhost:19093"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "runtimeVersion": "22",
      "showAsyncStacks": true,
      "outputCapture": "std",
      "sourceMaps": true,
      "cwd": "${workspaceRoot}/pods/server",
      "protocol": "inspector"
    },
    {
      "name": "Debug Fulltext",
      "type": "node",
      "request": "launch",
      "args": ["src/index.ts"],
      "env": {
        // "PORT": "4700",// For mongo
        "PORT": "4710", // for cockroach
        "FULLTEXT_DB_URL": "http://localhost:9201",
        // "DB_URL": "mongodb://localhost:27018",
        // "DB_URL": "postgresql://postgres:example@localhost:5432",
        "DB_URL": "postgresql://<EMAIL>:26258/defaultdb?sslmode=disable",
        "STORAGE_CONFIG": "minio|localhost?accessKey=minioadmin&secretKey=minioadmin",
        "SERVER_SECRET": "secret",
        "REKONI_URL": "http://localhost:4004",
        "MODEL_JSON": "${workspaceRoot}/models/all/bundle/model.json",
        "ELASTIC_INDEX_NAME": "local_storage_index",
        "REGION": "",
        "STATS_URL": "http://huly.local:4901",
        "ACCOUNTS_URL": "http://huly.local:3003",
        "QUEUE_CONFIG": "localhost:19093;-staging"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "runtimeVersion": "20",
      "showAsyncStacks": true,
      "outputCapture": "std",
      "sourceMaps": true,
      "cwd": "${workspaceRoot}/pods/fulltext",
      "protocol": "inspector"
    },
    {
      "name": "Debug Account",
      "type": "node",
      "request": "launch",
      "args": ["src/__start.ts"],
      "env": {
        "MONGO_URL": "mongodb://localhost:27017",
        // "DB_URL": "mongodb://localhost:27017",
        "DB_URL": "postgresql://<EMAIL>:26257/defaultdb?sslmode=disable",
        "SERVER_SECRET": "secret",
        "REGION_INFO": "|Mongo;cockroach|CockroachDB",
        "TRANSACTOR_URL": "ws://huly.local:3333,ws://huly.local:3332;;cockroach",
        "ACCOUNTS_URL": "http://localhost:3000",
        "ACCOUNT_PORT": "3000",
        // "FRONT_URL": "http://huly.local:8080",
        "FRONT_URL": "http://huly.local:8087",
        "STATS_URL": "http://huly.local:4900",
        "MAIL_URL": "",
        // "DB_NS": "account-2",
        // "WS_LIVENESS_DAYS": "1",
        // "WORKSPACE_LIMIT_PER_USER": "1",
        "MINIO_ACCESS_KEY": "minioadmin",
        "MINIO_SECRET_KEY": "minioadmin",
        "MINIO_ENDPOINT": "localhost"
        // "DISABLE_SIGNUP": "true",
      },
      "runtimeVersion": "20",
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "sourceMaps": true,
      "outputCapture": "std",
      "cwd": "${workspaceRoot}/pods/account",
      "protocol": "inspector"
    },
    {
      "name": "Debug Account(Staging)",
      "type": "node",
      "request": "launch",
      "args": ["src/__start.ts"],
      "env": {
        // "MONGO_URL": "mongodb://localhost:27018",
        // "DB_URL": "mongodb://localhost:27018",
        // "DB_URL": "postgresql://postgres:example@localhost:5432",
        "DB_URL": "postgresql://<EMAIL>:26258/defaultdb?sslmode=disable",
        "SERVER_SECRET": "secret",
        "REGION_INFO": "|Mongo;pg|Postgres;cockroach|CockroachDB",
        "TRANSACTOR_URL": "ws://transactor:3334;ws://localhost:3334",
        "ACCOUNTS_URL": "http://localhost:3003",
        "ACCOUNT_PORT": "3003",
        "FRONT_URL": "http://localhost:8083",
        "STATS_URL": "http://huly.local:4901",
        "MAIL_URL": "",
        // "DB_NS": "account-2",
        // "WS_LIVENESS_DAYS": "1",
        "MINIO_ACCESS_KEY": "minioadmin",
        "MINIO_SECRET_KEY": "minioadmin",
        "MINIO_ENDPOINT": "localhost"
        // "DISABLE_SIGNUP": "true",
      },
      "runtimeVersion": "20",
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "sourceMaps": true,
      "outputCapture": "std",
      "cwd": "${workspaceRoot}/pods/account",
      "protocol": "inspector"
    },
    {
      "name": "Debug Stats",
      "type": "node",
      "request": "launch",
      "args": ["src/__start.ts"],
      "env": {
        "PORT": "4901",
        "SERVER_SECRET": "secret"
      },
      "runtimeVersion": "20",
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "sourceMaps": true,
      "outputCapture": "std",
      "cwd": "${workspaceRoot}/pods/stats",
      "protocol": "inspector"
    },
    {
      "name": "Debug Workspace(mongo)",
      "type": "node",
      "request": "launch",
      "args": ["src/__start.ts"],
      "env": {
        "DB_URL": "mongodb://localhost:27017",
        // "DB_URL": "postgresql://postgres:example@localhost:5432",
        // "DB_URL": "postgresql://<EMAIL>:26257/defaultdb?sslmode=disable",

        "REGION": "",
        "SERVER_SECRET": "secret",
        "TRANSACTOR_URL": "ws://localhost:3333",
        "ACCOUNTS_URL": "http://localhost:3000",
        "FRONT_URL": "http://localhost:8080",
        "MAIL_URL": "",
        "MINIO_ACCESS_KEY": "minioadmin",
        "MINIO_SECRET_KEY": "minioadmin",
        "MINIO_ENDPOINT": "localhost",
        "MODEL_VERSION": "v0.7.75",
        "WS_OPERATION": "all+backup",
        "BACKUP_STORAGE": "minio|minio?accessKey=minioadmin&secretKey=minioadmin",
        "BACKUP_BUCKET": "dev-backups",
        "INIT_REPO_DIR": "${workspaceRoot}/pods/workspace/init",
        "INIT_WORKSPACE": "staging-dev",
        "QUEUE_CONFIG": "huly.local:19092",
        "QUEUE_REGION": ""
      },
      "runtimeVersion": "20",
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "sourceMaps": true,
      "outputCapture": "std",
      "cwd": "${workspaceRoot}/pods/workspace",
      "protocol": "inspector"
    },
    {
      "name": "Debug Workspace(cockroach)",
      "type": "node",
      "request": "launch",
      "args": ["src/__start.ts"],
      "env": {
        // "DB_URL": "mongodb://localhost:27017",
        // "DB_URL": "postgresql://postgres:example@localhost:5432",
        "DB_URL": "postgresql://<EMAIL>:26257/defaultdb?sslmode=disable",
        "FULLTEXT_URL": "http://huly.local:4702",
        "REGION": "cockroach",
        "SERVER_SECRET": "secret",
        "TRANSACTOR_URL": "ws://localhost:3332",
        "ACCOUNTS_URL": "http://localhost:3000",
        "FRONT_URL": "http://localhost:8080",
        "MAIL_URL": "",
        "STORAGE_CONFIG": "datalake|http://localhost:4030",
        "MODEL_VERSION": "0.7.153",
        "WS_OPERATION": "all+backup",
        "BACKUP_STORAGE": "minio|minio?accessKey=minioadmin&secretKey=minioadmin",
        "BACKUP_BUCKET": "dev-backups",
        "QUEUE_CONFIG": "huly.local:19092"
        // "INIT_REPO_DIR": "${workspaceRoot}/pods/workspace/init",
        // "INIT_WORKSPACE": "staging-dev"
      },
      "runtimeVersion": "20",
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "sourceMaps": true,
      "outputCapture": "std",
      "cwd": "${workspaceRoot}/pods/workspace",
      "protocol": "inspector"
    },
    {
      "name": "Debug Front",
      "type": "node",
      "request": "launch",
      "args": ["src/__start.ts"],
      "env": {
        "MONGO_URL": "mongodb://localhost:27017",
        "METRICS_CONSOLE": "false",
        "STORAGE_CONFIG": "minio|localhost?accessKey=minioadmin&secretKey=minioadmin",
        "SERVER_SECRET": "secret",
        "REKONI_URL": "http://localhost:4004",
        "FRONT_URL": "http://localhost:8080",
        "ACCOUNTS_URL": "http://localhost:3000",
        "UPLOAD_URL": "/files",
        "SERVER_PORT": "8087",
        "COLLABORATOR_URL": "ws://localhost:3078",
        "CALENDAR_URL": "http://localhost:8095",
        "GMAIL_URL": "http://localhost:8088",
        "TELEGRAM_URL": "http://localhost:8086",
        "MODEL_VERSION": "",
        "VERSION": ""
      },
      "runtimeVersion": "20",
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "showAsyncStacks": true,
      "sourceMaps": true,
      "cwd": "${workspaceRoot}/pods/front",
      "protocol": "inspector",
      "outputCapture": "std"
    },
    {
      "name": "Debug Collaborator",
      "type": "node",
      "request": "launch",
      "args": ["src/__start.ts"],
      "env": {
        "SECRET": "secret",
        "METRICS_CONSOLE": "true",
        "ACCOUNTS_URL": "http://localhost:3000",
        "MONGO_URL": "mongodb://localhost:27017",
        "STORAGE_CONFIG": "datalake|http://localhost:4030"
      },
      "runtimeVersion": "20",
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "sourceMaps": true,
      "cwd": "${workspaceRoot}/pods/collaborator",
      "protocol": "inspector"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Jest tests",
      "program": "${fileDirname}/../../node_modules/@rushstack/heft/lib/start.js",
      "cwd": "${fileDirname}/../../",
      "args": ["--debug", "test", "--clean", "--test-path-pattern", "${file}"],
      "console": "integratedTerminal",
      "sourceMaps": true,
      "protocol": "inspector"
    },
    {
      "name": "Debug generator",
      "type": "node",
      "request": "launch",
      "args": ["src/index.ts", "gen-recruit", "ws1", "20"],
      "env": {
        "ACCOUNTS_URL": "ws://localhost:3000",
        "MINIO_ACCESS_KEY": "minioadmin",
        "MINIO_SECRET_KEY": "minioadmin",
        "MINIO_ENDPOINT": "localhost"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "sourceMaps": true,
      "cwd": "${workspaceRoot}/dev/generator",
      "protocol": "inspector"
    },
    {
      "name": "Debug backup tool",
      "type": "node",
      "request": "launch",
      "args": [
        "src/__start.ts",
        "backup-restore",
        "/Users/<USER>/Develop/private/platform/tests/sanity-ws",
        "sanity-ws",
        "--upgrade"
      ],
      "env": {
        "MODEL_VERSION": "0.7.153",
        "STORAGE_CONFIG": "datalake|http://huly.local:4030",
        "ACCOUNTS_URL": "http://localhost:3003",
        "TRANSACTOR_URL": "ws://localhost:3334",
        "ACCOUNT_DB_URL": "postgresql://root@localhost:26258/defaultdb?sslmode=disable",
        "MONGO_URL": "mongodb://localhost:27018",
        "ELASTIC_URL": "http://localhost:9201",
        "SERVER_SECRET": "secret",
        "DB_URL": "postgresql://root@localhost:26258/defaultdb?sslmode=disable",
        "QUEUE_CONFIG": "localhost:19093;-staging"
      },
      "smartStep": false,
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "sourceMaps": true,
      "sourceMapRenames": false,
      "skipFiles": ["<node_internals>/**"],
      "cwd": "${workspaceRoot}/dev/tool",
      "protocol": "inspector",
      "outputCapture": "std",
      "runtimeVersion": "22",
      "showAsyncStacks": true
    },
    {
      "name": "Debug tool upgrade",
      "type": "node",
      "request": "launch",
      "args": ["src/__start.ts", "migrate-github-account", "--region", "cockroach", "--db", "%github"],
      "env": {
        "SERVER_SECRET": "secret",
        "MINIO_ACCESS_KEY": "minioadmin",
        "MINIO_SECRET_KEY": "minioadmin",
        "MINIO_ENDPOINT": "localhost:9000",
        "TRANSACTOR_URL": "ws://localhost:3333",
        "MONGO_URL": "mongodb://localhost:27017",
        "DB_URL": "mongodb://localhost:27017",
        "ACCOUNTS_URL": "http://127.0.0.1:3000",
        "ACCOUNT_DB_URL": "mongodb://localhost:27017",
        "TELEGRAM_DATABASE": "telegram-service",
        "REKONI_URL": "http://localhost:4004",
        "MODEL_VERSION": "0.7.1"
      },
      "runtimeVersion": "20",
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "sourceMaps": true,
      "outputCapture": "std",
      "cwd": "${workspaceRoot}/dev/tool"
    },
    {
      "name": "Debug tool upgrade PG(Cockroach)",
      "type": "node",
      "request": "launch",
      "args": ["src/__start.ts", "upgrade-workspace", "platform"],
      "env": {
        "SERVER_SECRET": "secret",
        "MINIO_ACCESS_KEY": "minioadmin",
        "MINIO_SECRET_KEY": "minioadmin",
        "MINIO_ENDPOINT": "localhost:9000",
        "TRANSACTOR_URL": "ws://localhost:3332",
        "ACCOUNTS_URL": "http://localhost:3000",
        "ACCOUNT_DB_URL": "postgresql://<EMAIL>:26257/defaultdb?sslmode=disable",
        // "ACCOUNT_DB_URL": "postgresql://postgres:example@localhost:5433",
        // "DB_URL": "postgresql://postgres:example@localhost:5433",
        "DB_URL": "postgresql://<EMAIL>:26257/defaultdb?sslmode=disable",
        "MONGO_URL": "mongodb://localhost:27017",
        "TELEGRAM_DATABASE": "telegram-service",
        "REKONI_URL": "http://localhost:4004",
        "MODEL_VERSION": "0.7.75"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "sourceMaps": true,
      "outputCapture": "std",
      "cwd": "${workspaceRoot}/dev/tool"
    },
    {
      "name": "Debug tool move",
      "type": "node",
      "request": "launch",
      "args": ["src/__start.ts", "move-to-pg"],
      "env": {
        "SERVER_SECRET": "secret",
        "MINIO_ACCESS_KEY": "minioadmin",
        "MINIO_SECRET_KEY": "minioadmin",
        "MINIO_ENDPOINT": "localhost",
        "TRANSACTOR_URL": "ws://localhost:3333",
        "MONGO_URL": "mongodb://localhost:27017",
        "DB_URL": "postgresql://postgres:example@localhost:5432",
        "ACCOUNTS_URL": "http://localhost:3000",
        "TELEGRAM_DATABASE": "telegram-service",
        "REKONI_URL": "http://localhost:4004"
      },
      "runtimeVersion": "20",
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "sourceMaps": true,
      "outputCapture": "std",
      "cwd": "${workspaceRoot}/dev/tool"
    },
    {
      "name": "Debug backup",
      "type": "node",
      "request": "launch",
      "args": ["src/index.ts"],
      "env": {
        "ACCOUNTS_URL": "http://localhost:3000",
        "STORAGE": "minio|localhost?accessKey=minioadmin&secretKey=minioadmin",
        "WORKSPACE_STORAGE": "minio|localhost?accessKey=minioadmin&secretKey=minioadmin",
        "DB_URL": "postgresql://<EMAIL>:26257/defaultdb?sslmode=disable",
        "MODEL_JSON": "${workspaceRoot}/models/all/bundle/model.json",
        "SECRET": "secret",
        "REGION": "cockroach",
        "BUCKET_NAME": "backups",
        "INTERVAL": "300"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "showAsyncStacks": true,
      "sourceMaps": true,
      "outputCapture": "std",
      "cwd": "${workspaceRoot}/pods/backup",
      "protocol": "inspector"
    },
    {
      "name": "Debug backup API",
      "type": "node",
      "request": "launch",
      "args": ["src/index.ts"],
      "env": {
        "ACCOUNTS_URL": "http://localhost:3000",
        "STORAGE": "minio|localhost?accessKey=minioadmin&secretKey=minioadmin",
        "SECRET": "secret",
        "BUCKET_NAME": "backups"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "showAsyncStacks": true,
      "sourceMaps": true,
      "outputCapture": "std",
      "cwd": "${workspaceRoot}/services/backup/backup-api-pod",
      "protocol": "inspector"
    },
    {
      "name": "Debug archive tool",
      "type": "node",
      "request": "launch",
      "args": ["src/__start.ts", "archive-workspaces-mongo", "--timeout", "7", "--remove"],
      "env": {
        "ACCOUNTS_URL": "http://localhost:3000",
        "STORAGE": "minio|localhost?accessKey=minioadmin&secretKey=minioadmin",
        "WORKSPACE_STORAGE": "minio|localhost?accessKey=minioadmin&secretKey=minioadmin",
        "MONGO_URL": "mongodb://localhost:27017",
        "DB_URL": "mongodb://localhost:27017",
        "SERVER_SECRET": "secret",
        "SECRET": "secret",
        "BUCKET_NAME": "backups"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "showAsyncStacks": true,
      "sourceMaps": true,
      "outputCapture": "std",
      "cwd": "${workspaceRoot}/dev/tool",
      "protocol": "inspector"
    },
    {
      "name": "Debug restore-all tool",
      "type": "node",
      "request": "launch",
      "args": ["src/__start.ts", "restore-all"],
      "env": {
        "ACCOUNTS_URL": "http://localhost:3000",
        "STORAGE": "minio|localhost?accessKey=minioadmin&secretKey=minioadmin",
        // "DB_URL": "mongodb://localhost:27017",
        "DB_URL": "postgresql://<EMAIL>:26258/defaultdb?sslmode=disable",
        "SERVER_SECRET": "secret",
        "BUCKET_NAME": "dev-backups"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "showAsyncStacks": true,
      "sourceMaps": true,
      "outputCapture": "std",
      "cwd": "${workspaceRoot}/dev/tool",
      "protocol": "inspector"
    },
    {
      "name": "Debug Github integration",
      "type": "node",
      "request": "launch",
      "args": ["src/index.ts"],
      "env": {
        "SERVER_SECRET": "secret",
        "ACCOUNTS_URL": "http://localhost:3000",
        "APP_ID": "${env:POD_GITHUB_APPID}",
        "CLIENT_ID": "${env:POD_GITHUB_CLIENTID}",
        "CLIENT_SECRET": "${env:POD_GITHUB_CLIENT_SECRET}",
        "PRIVATE_KEY": "${env:POD_GITHUB_PRIVATE_KEY}",
        "COLLABORATOR_URL": "ws://huly.local:3078",
        "STORAGE_CONFIG": "datalake|http://huly.local:4030",
        "PLATFORM_OPERATION_LOGGING": "true",
        "FRONT_URL": "http://localhost:8080",
        "PORT": "3500",
        "STATS_URL": "http://huly.local:4900"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "sourceMaps": true,
      "cwd": "${workspaceRoot}/services/github/pod-github",
      "protocol": "inspector",
      "outputCapture": "std"
    },
    {
      "name": "Debug qms workers",
      "type": "node",
      "request": "launch",
      "args": ["src/index.ts"],
      "env": {
        "ACCOUNTS_URL": "http://localhost:3000",
        "SECRET": "secret",
        "DOCS_RELEASE_INTERVAL": "10000",
        "DOCS_IN_REVIEW_CHECK_INTERVAL": "10000",
        "DOCS_REVIEW_PERIOD_IN_MONTHS": "1"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "showAsyncStacks": true,
      "sourceMaps": true,
      "cwd": "${workspaceRoot}/pods/workers"
    },
    {
      "name": "Debug doc import tool",
      "type": "node",
      "request": "launch",
      "args": ["src/__start.ts", "import", "--spec", "toc.json", "docx", "ws", "person-owner"],
      "env": {
        "SERVER_SECRET": "secret",
        "ACCOUNTS_URL": "http://localhost:3000",
        "COLLABORATOR_URL": "ws://localhost:3078",
        "STORAGE_CONFIG": "minio|localhost?accessKey=minioadmin&secretKey=minioadmin",
        "MONGO_URL": "mongodb://localhost:27017"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "runtimeVersion": "20",
      "showAsyncStacks": true,
      "sourceMaps": true,
      "cwd": "${workspaceRoot}/dev/doc-import-tool",
      "protocol": "inspector"
    },
    {
      "name": "Debug print",
      "type": "node",
      "request": "launch",
      "args": ["src/index.ts"],
      "env": {
        "PORT": "4005",
        "SECRET": "secret",
        "MONGO_URL": "mongodb://localhost:27017",
        "STORAGE_CONFIG": "minio|localhost?accessKey=minioadmin&secretKey=minioadmin"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "runtimeVersion": "20",
      "sourceMaps": true,
      "cwd": "${workspaceRoot}/services/print/pod-print"
    },
    {
      "name": "Debug sign",
      "type": "node",
      "request": "launch",
      "args": ["src/index.ts"],
      "env": {
        "PORT": "4006",
        "SECRET": "secret",
        "CERTIFICATE_PATH": "${workspaceRoot}/services/sign/pod-sign/debug/certificate.p12",
        "MONGO_URL": "mongodb://localhost:27017",
        "MINIO_ENDPOINT": "localhost",
        "MINIO_ACCESS_KEY": "minioadmin",
        "MINIO_SECRET_KEY": "minioadmin",
        "SERVICE_ID": "sign-service",
        "ACCOUNTS_URL": "http://localhost:3000",
        "BRANDING_PATH": "${workspaceRoot}/services/sign/pod-sign/debug/branding.json"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "runtimeVersion": "20",
      "sourceMaps": true,
      "cwd": "${workspaceRoot}/services/sign/pod-sign"
    },
    {
      "name": "Debug rekoni",
      "type": "node",
      "request": "launch",
      "args": ["src/index.ts"],
      "env": {
        "PORT": "4004",
        "SERVICE_ID": "rekoni-service",
        "SECRET": "secret"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "sourceMaps": true,
      "cwd": "${workspaceRoot}/services/rekoni",
      "protocol": "inspector"
    },
    {
      "name": "Debug analytics collector",
      "type": "node",
      "request": "launch",
      "args": ["src/index.ts"],
      "env": {
        "PORT": "4017",
        "SECRET": "secret",
        "MONGO_URL": "mongodb://localhost:27017",
        "MINIO_ENDPOINT": "localhost",
        "MINIO_ACCESS_KEY": "minioadmin",
        "MINIO_SECRET_KEY": "minioadmin",
        "SERVER_SECRET": "secret",
        "SERVICE_ID": "analytics-collector",
        "ACCOUNTS_URL": "http://localhost:3000"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "runtimeVersion": "20",
      "sourceMaps": true,
      "showAsyncStacks": true,
      "outputCapture": "std",
      "cwd": "${workspaceRoot}/services/analytics-collector/pod-analytics-collector"
    },
    {
      "name": "Debug calendar",
      "type": "node",
      "request": "launch",
      "args": ["src/index.ts"],
      "env": {
        "ACCOUNTS_URL": "http://localhost:3000",
        "Credentials": "",
        "KVS_URL": "http://localhost:8094",
        "SECRET": "secret",
        "WATCH_URL": "https://calendar.hc.engineering/push"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "runtimeVersion": "20",
      "sourceMaps": true,
      "showAsyncStacks": true,
      "outputCapture": "std",
      "cwd": "${workspaceRoot}/services/calendar/pod-calendar"
    },
    {
      "name": "Debug AI bot",
      "type": "node",
      "request": "launch",
      "args": ["src/index.ts"],
      "env": {
        "ACCOUNTS_URL": "http://localhost:3000",
        "MONGO_URL": "mongodb://localhost:27017",
        "PORT": "4010",
        "SERVER_SECRET": "secret",
        "FIRST_NAME": "Jolie",
        "LAST_NAME": "AI",
        "PASSWORD": "password",
        "AVATAR_PATH": "./assets/avatar.png",
        "AVATAR_CONTENT_TYPE": ".png",
        "STORAGE_CONFIG": "minio|localhost?accessKey=minioadmin&secretKey=minioadmin",
        "LOVE_ENDPOINT": "http://localhost:8096"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "sourceMaps": true,
      "runtimeVersion": "20",
      "cwd": "${workspaceRoot}/services/ai-bot/pod-ai-bot",
      "protocol": "inspector",
      "outputCapture": "std"
    },
    {
      "name": "Debug telegram bot",
      "type": "node",
      "request": "launch",
      "args": ["src/index.ts"],
      "env": {
        "PORT": "4020",
        "BOT_TOKEN": "token",
        "MONGO_URL": "mongodb://localhost:27017",
        "MONGO_DB": "telegram-bot",
        "SECRET": "secret",
        "ACCOUNTS_URL": "http://huly.local:3000",
        "SERVICE_ID": "telegram-bot-service",
        "MINIO_ACCESS_KEY": "minioadmin",
        "MINIO_SECRET_KEY": "minioadmin",
        "MINIO_ENDPOINT": "localhost",
        "QUEUE_CONFIG": "localhost:19092",
        "QUEUE_REGION": "cockroach",
        "DB_URL": "postgresql://<EMAIL>:26257/defaultdb?sslmode=disable"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "runtimeVersion": "20",
      "sourceMaps": true,
      "cwd": "${workspaceRoot}/services/telegram-bot/pod-telegram-bot"
    },
    {
      "name": "Debug datalake",
      "type": "node",
      "request": "launch",
      "args": ["src/index.ts"],
      "env": {
        "PORT": "4030",
        "SECRET": "secret",
        "DB_URL": "postgresql://root@localhost:26257/defaultdb?sslmode=disable",
        "BUCKETS": "blobs,eu|http://localhost:9000?accessKey=minioadmin&secretKey=minioadmin",
        "ACCOUNTS_URL": "http://localhost:3000",
        "STATS_URL": "http://huly.local:4900",
        "QUEUE_CONFIG": "localhost:19092"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "runtimeVersion": "20",
      "sourceMaps": true,
      "cwd": "${workspaceRoot}/services/datalake/pod-datalake"
    },
    {
      "name": "Debug media",
      "type": "node",
      "request": "launch",
      "args": ["src/index.ts"],
      "env": {
        "ACCOUNTS_URL": "http://huly.local:3000",
        "SECRET": "secret",
        "REGION": "cockroach",
        "QUEUE_CONFIG": "localhost:19092"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "runtimeVersion": "20",
      "sourceMaps": true,
      "cwd": "${workspaceRoot}/pods/media"
    },
    {
      "type": "chrome",
      "name": "Attach to Browser",
      "request": "attach",
      "urlFilter": "http://localhost:8080/*",
      "port": 9222,
      "sourceMapPathOverrides": {
        "webpack://*": "${workspaceFolder}/*"
      }
    },
    {
      "name": "Debug Notion import",
      "type": "node",
      "request": "launch",
      "args": [
        "src/__start.ts",
        "import-notion-to-teamspace",
        "/home/<USER>/work/notion/natalya/Export-fad9ecb4-a1a5-4623-920d-df32dd423743",
        "-u",
        "user1",
        "-pw",
        "1234",
        "-ws",
        "ws5",
        "-ts",
        "notion"
      ],
      // "args": ["src/__start.ts", "import-notion-with-teamspaces", "/home/<USER>/work/notion/natalya/Export-fad9ecb4-a1a5-4623-920d-df32dd423743", "-u", "user1", "-pw", "1234", "-ws", "ws1"],
      "env": {
        "FRONT_URL": "http://localhost:8087"
      },
      "runtimeVersion": "20",
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "sourceMaps": true,
      "outputCapture": "std",
      "cwd": "${workspaceRoot}/dev/import-tool"
    },
    {
      "name": "Debug ClickUp import",
      "type": "node",
      "request": "launch",
      "args": [
        "src/__start.ts",
        "import-clickup-tasks",
        "/home/<USER>/work/clickup/aleksandr/debug/mentions.csv",
        "-u",
        "user1",
        "-pw",
        "1234",
        "-ws",
        "ws10"
      ],
      "env": {
        "FRONT_URL": "http://localhost:8087"
      },
      "runtimeVersion": "20",
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "sourceMaps": true,
      "outputCapture": "std",
      "cwd": "${workspaceRoot}/dev/import-tool"
    },
    {
      "name": "Debug Huly import",
      "type": "node",
      "request": "launch",
      "args": [
        "src/__start.ts",
        "import",
        "/home/<USER>/huly/platform/dev/import-tool/docs/huly/example-workspace",
        "-u",
        "user1",
        "-pw",
        "1234",
        "-ws",
        "ws1"
      ],
      "env": {
        "FRONT_URL": "http://localhost:8087"
      },
      "runtimeVersion": "20",
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "sourceMaps": true,
      "outputCapture": "std",
      "cwd": "${workspaceRoot}/dev/import-tool"
    },
    {
      "name": "Debug Export service",
      "type": "node",
      "request": "launch",
      "args": ["src/index.ts"],
      "env": {
        "PORT": "4009",
        "SECRET": "secret",
        "ACCOUNTS_URL": "http://localhost:3000",
        "STORAGE_CONFIG": "datalake|http://huly.local:4030",
        "TRANSACTOR_URL": "ws://localhost:3333",
        "SERVICE_ID": "export-service"
      },
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "sourceMaps": true,
      "cwd": "${workspaceRoot}/services/export/pod-export"
    },
    {
      "name": "Msg2File",
      "type": "node",
      "request": "launch",
      "args": ["src/index.ts"],
      "env": {
        "ACCOUNTS_URL": "http://localhost:3000",
        "DB_URL": "postgresql://root@localhost:26257/defaultdb?sslmode=disable",
        "PORT": "9087",
        "SECRET": "secret",
        "SERVICE_ID": "msg2file-service",
        "STORAGE_CONFIG": "datalake|http://huly.local:4030"
      },
      "runtimeVersion": "20",
      "runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
      "sourceMaps": true,
      "outputCapture": "std",
      "cwd": "${workspaceRoot}/services/msg2file"
    }
  ]
}
